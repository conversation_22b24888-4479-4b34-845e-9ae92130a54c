---
alwaysApply: true
description: Git提交规范和工作流程指南
---

# Git提交规范

## 1. 提交信息格式规范

### 标准格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型 (type)
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响代码逻辑）
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动
- **ci**: CI/CD相关更改
- **build**: 构建系统或外部依赖更改

### 作用域 (scope)
- 可选，表示提交影响的范围
- 例如：`feat(auth): 添加用户登录功能`
- 例如：`fix(api): 修复用户列表接口错误`

### 主题 (subject)
- 简短描述，不超过50个字符
- 使用祈使句，如"添加"、"修复"、"更新"
- 首字母小写，不加句号

### 示例
```
feat(user): 添加用户注册功能
fix(api): 修复登录接口500错误
docs(readme): 更新项目说明文档
style(components): 统一代码格式
refactor(utils): 重构日期处理函数
```

## 2. 分支命名规范

### 主分支
- `main` 或 `master`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试

### 功能分支
- `feature/功能名称`: 新功能开发
- `bugfix/问题描述`: 修复bug
- `hotfix/紧急修复`: 紧急修复
- `release/版本号`: 发布版本

### 示例
```
feature/user-authentication
bugfix/login-validation-error
hotfix/security-vulnerability
release/v1.2.0
```

## 3. 工作流程

### 开发流程
1. 从 `master` 分支创建功能分支
2. 在功能分支上开发
3. 提交代码时遵循提交信息规范
4. 完成开发后创建Pull Request
5. 代码审查通过后合并到 `master` 分支

### 发布流程
1. 从 `master` 分支创建 `hotfix` 分支
2. 在 `hotfix` 分支上进行测试和修复
3. 测试通过后合并到 `master` 分支
4. 打标签标记版本

### 紧急修复流程
1. 从 `master` 分支创建 `hotfix` 分支
2. 修复问题并测试
3. 合并到 `master` 分支
4. 打标签标记版本

## 4. 提交前检查

### 代码质量
- 运行测试确保通过
- 检查代码格式和lint规则
- 确保没有console.log等调试代码

### 提交前命令
```bash
# 运行测试
npm test

# 检查代码格式
npm run lint

# 格式化代码
npm run format

# 添加所有更改
git add .

# 提交代码
git commit -m "type(scope): subject"
```

## 5. 常用Git命令

### 基础操作
```bash
# 查看状态
git status

# 查看分支
git branch

# 切换分支
git checkout <branch-name>

# 创建并切换分支
git checkout -b <branch-name>

# 拉取最新代码
git pull origin <branch-name>

# 推送代码
git push origin <branch-name>
```

### 提交相关
```bash
# 查看提交历史
git log --oneline

# 查看文件变更
git diff

# 暂存更改
git stash

# 恢复暂存
git stash pop

# 撤销最后一次提交
git reset --soft HEAD~1
```

## 6. 注意事项

- 每次提交只做一件事，保持提交的原子性
- 提交信息要清晰明了，便于后续维护
- 定期同步主分支代码，避免冲突
- 重要功能开发完成后及时创建Pull Request
- 代码审查要认真对待，确保代码质量
