import React from 'react'
import { Enhance<PERSON>ield } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { Button, Dropdown, Modal } from '@hose/eui'
import type { MenuProps } from '@hose/eui'
import { OutlinedTipsWarning, OutlinedTipsAdd } from '@hose/eui-icons'
import { app } from '@ekuaibao/whispered'
import { StaffIF } from '@ekuaibao/ekuaibao_types'
import EBusinessCardComponent from '../../elements/e-card/ECardElement'
import { Fetch } from '@ekuaibao/fetch'
import { showMessage } from '@ekuaibao/show-util'

const CARD_TYPES = {
  ALIPAY: 'ALIPAY',
  LL: 'LL'
} as const

const ECARD_TYPES = {
  DOMESTIC: 'domestic',
  INTERNATIONAL: 'international'
} as const

type CardType = keyof typeof CARD_TYPES
type ECardType = typeof ECARD_TYPES[keyof typeof ECARD_TYPES]

interface ICorporateExpenseCard {
  offlineAuthorized: boolean
  id?: string | null
  cardNo?: string | null
  cardType?: string | null
}

interface IECardOptionalRange {
  domestic?: {
    alipay?: boolean
  }
  international?: {
    ll?: {
      checked?: boolean
    }
  }
}

interface IProps {
  value?: ICorporateExpenseCard
  submitterId: StaffIF | string
  onChange: (value: ICorporateExpenseCard) => void
  field?: {
    eCardOptionalRange?: IECardOptionalRange
  }
}

interface IState {
  eCardtype?: ECardType
  loading?: boolean
}

@((EnhanceField as any)({
  descriptor: {
    type: 'corporateExpenseCard'
  },
  wrapper: wrapper()
}))
export default class EBusinessCard extends React.Component<IProps, IState> {
  constructor(props){
    super(props)
    this.state = {
      loading: false,
      eCardtype: props?.value?.cardType === CARD_TYPES.ALIPAY ? ECARD_TYPES.DOMESTIC : ECARD_TYPES.INTERNATIONAL,
    }
  }

  private getCardTypeAvailability = (eCardOptionalRange?: IECardOptionalRange) => {
    return {
      [CARD_TYPES.ALIPAY]: eCardOptionalRange?.domestic?.alipay ?? false,
      [CARD_TYPES.LL]: eCardOptionalRange?.international?.ll?.checked ?? false
    }
  }

  private createEmptyCardData = (): ICorporateExpenseCard => ({
    offlineAuthorized: false,
    id: null,
    cardNo: null,
    cardType: null
  })

  handleClick = async (cardType: CardType): Promise<void> => {
    const { onChange, submitterId } = this.props

    this.setState({
      eCardtype: cardType === CARD_TYPES.ALIPAY ? ECARD_TYPES.DOMESTIC : ECARD_TYPES.INTERNATIONAL,
      loading: true
    })

    try {
      const ownerIdStr = typeof submitterId === 'string' ? submitterId : submitterId?.id
      const { data } = await app.invokeService('@bills:check:ecard:type:state', {
        mustAvailable: 'true',
        openUserId: ownerIdStr,
        openCorpId: Fetch.ekbCorpId,
        cardType
      })

      const cardInfo = data?.[0]

      if (cardInfo?.id) {
        const { id, cardNo, cardType: responseCardType } = cardInfo
        onChange({
          offlineAuthorized: true,
          id,
          cardNo,
          cardType: responseCardType
        })
      } else {
        onChange(this.createEmptyCardData())
        const errorMsg = data?.msg || i18n.get('请联系管理员进行授权并激活卡片')
        showMessage.error(errorMsg)
      }
    } catch (error) {
      onChange(this.createEmptyCardData())
      Modal.error({
        title: error instanceof Error ? error.message : i18n.get('请联系管理员进行授权并激活卡片')
      })
    } finally {
      this.setState({ loading: false })
    }
  }


  handleDel = (): void => {
    const { onChange } = this.props
    onChange(this.createEmptyCardData())
  }

  private renderECardType = () => {
    const { field } = this.props
    const { loading } = this.state
    const eCardOptionalRange = field?.eCardOptionalRange
    const cardAvailability = this.getCardTypeAvailability(eCardOptionalRange)

    const { ALIPAY: domesticAvailable, LL: internationalAvailable } = cardAvailability

    // 两种类型都可用时显示下拉菜单
    if (domesticAvailable && internationalAvailable) {
      const items: MenuProps['items'] = [
        {
          key: ECARD_TYPES.DOMESTIC,
          label: (
            <a onClick={() => this.handleClick(CARD_TYPES.ALIPAY)}>
              {i18n.get('国内消费')}
            </a>
          ),
        },
        {
          key: ECARD_TYPES.INTERNATIONAL,
          label: (
            <a onClick={() => this.handleClick(CARD_TYPES.LL)}>
              {i18n.get('国际消费')}
            </a>
          ),
        },
      ]

      return (
        <Dropdown
          overlayStyle={{ width: '140px' }}
          menu={{ items }}
          placement="bottomLeft"
        >
          <Button
            category="secondary"
            theme="highlight"
            size="small"
            icon={<OutlinedTipsAdd />}
            loading={loading}
          >
            {i18n.get('添加易商卡')}
          </Button>
        </Dropdown>
      )
    }

    // 仅国内可用
    if (domesticAvailable) {
      return (
        <Button
          category="secondary"
          theme="highlight"
          size="small"
          icon={<OutlinedTipsAdd />}
          onClick={() => this.handleClick(CARD_TYPES.ALIPAY)}
          loading={loading}
        >
          {i18n.get('易商卡国内消费')}
        </Button>
      )
    }

    // 仅国际可用
    if (internationalAvailable) {
      return (
        <Button
          category="secondary"
          theme="highlight"
          size="small"
          icon={<OutlinedTipsAdd />}
          onClick={() => this.handleClick(CARD_TYPES.LL)}
          loading={loading}
        >
          {i18n.get('易商卡国际消费')}
        </Button>
      )
    }

    // 都不可用时显示禁用状态
    return (
      <>
        <Button
          disabled
          category="secondary"
          theme="highlight"
          size="small"
          icon={<OutlinedTipsAdd />}
        >
          {i18n.get('添加易商卡')}
        </Button>
        <div style={{
          color: 'var(--eui-text-caption)',
          font: 'var(--eui-font-body-r1)',
          marginTop: 4
        }}>
          <OutlinedTipsWarning style={{ marginRight: 4 }} />
          <span>{i18n.get('请联系管理员配置卡片类型')}</span>
        </div>
      </>
    )
  }

  render() {
    const { value } = this.props
    const { eCardtype } = this.state

    return value?.offlineAuthorized ? (
      <EBusinessCardComponent
        eCardtype={eCardtype}
        onDel={this.handleDel}
      />
    ) : (
      this.renderECardType()
    )
  }
}
