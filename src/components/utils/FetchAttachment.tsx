import React, { PureComponent } from 'react'
import { isArray, isObject } from '@ekuaibao/helpers'
import { Fetch } from '@ekuaibao/fetch'
import { Resource } from '@ekuaibao/fetch'

const aiResult = new Resource('/api/extend/ai')

interface ElemTypeIF {
  type: string
  entity: string
}

interface DataTypeIF {
  type: string
  elemType: ElemTypeIF
}

interface FieldIF {
  dataType: DataTypeIF
}

interface Props {
  value: AttachmentIF[]
  onChange: (value: string | any[]) => void
  field: FieldIF
}

export interface AttachmentIF {
  fileId: string
  fileName: string
  key: string
  url?: string
}

export interface AttachmentResponseIF {
  id: string
  key: string
  url: string
  thumbUrl: string
}

export const FetchAttachment = <P extends Props>(WrappedComponent: React.ComponentType<P>) => {
  return class extends PureComponent<P> {
    constructor(props: P) {
      super(props)
      const value = props.value
      this.state = { value }
    }

    async componentWillReceiveProps(nextProps: Readonly<P>) {
      if (nextProps.value !== this.props.value && needFetchData(nextProps)) {
        const { value, onChange } = nextProps
        const values = await fetchAttachment(value, true)
        this.setState({ value: values }, () => onChange && onChange(values))
      }
    }

    async componentDidMount() {
      const { value, onChange } = this.props
      if (needFetchData(this.props)) {
        const values = await fetchAttachment(value)
        this.setState({ value: values }, () => onChange && onChange(values))
      }
    }

    render(): React.ReactNode {
      let newProps = {}
      if (needFetchData(this.props)) {
        newProps = { ...this.state }
      }
      return <WrappedComponent {...this.props} {...newProps} />
    }
  }
}

export async function fetchAttachment(value: AttachmentIF[], isUpdate = false) {
  if (!value) {
    return value
  }
  const { fileIds, fileMap } = getFileIds(value, isUpdate)
  if (fileIds.length) {
    const res = await Fetch.POST(`/api/v1/attachment/attachments/ids`, null, { body: { ids: fileIds } }, {
      hiddenLoading: true
    } as any)

    return fileIds.reduce((arr, id) => {
      const item = res.items.find((v: AttachmentResponseIF) => v.id === id)
      return item ? arr.concat({ ...fileMap[item.id], fileId: item }) : arr
    }, [])
  }
  return Promise.resolve(value)
}

function needFetchData(props: Props): boolean {
  const { field, value } = props
  if (!value || !field) {
    return false
  }
  if (field.dataType.type !== 'list' && field.dataType.elemType.type !== 'attachment') {
    return false
  }
  if (isArray(value)) {
    const hasDingPan = (value as AttachmentIF[]).find((line: AttachmentIF) => line.key?.startsWith('DP:'))
    if (hasDingPan) {
      return false
    }
    return !!(value as AttachmentIF[]).find((line: AttachmentIF) => !isObject(line.fileId) && !line.url)
  }
  return false
}

function getFileIds(values: AttachmentIF[], isUpdate: boolean) {
  if (!values || !values.length) {
    return { fileIds: [], fileMap: {} }
  }
  const fileIds: string[] = []
  const fileMap: { [key: string]: AttachmentIF } = {}
  // 初始化过滤掉盯盘数据不再后台请求确认数据（为了修复有盯盘数据时再上传盯盘数据被清除问题）
  values
    .filter(value => !value.key.startsWith('DP:') && !isUpdate)
    .forEach(line => {
      if (line.fileId && !isObject(line.fileId)) {
        fileIds.push(line.fileId)
        fileMap[line.fileId] = line
      }
    })
  return { fileIds, fileMap }
}

export const fetchAttachmentAIResult = async (attachmentKey: string, fields: { label: string, type: string, isArray?: boolean }[], datalinkFieldMap: { [key: string]: { label: string, type: string, isArray?: boolean }[] }) => {
  // 首先调用原始接口获取 ID
  const initialResponse = await aiResult.POST(`/flow/autoFill`, {
    key: attachmentKey,
    fields,
    datalinkFieldMap
  })
  
  const { id } = initialResponse
  if (!id) {
    throw new Error('AI 接口返回的 ID 为空')
  }
  
  // 轮询获取结果，最多轮询3分钟（300秒）
  const maxDuration = 3 * 60 * 1000 // 3分钟
  const pollInterval = 6000 // 6秒轮询一次
  const startTime = Date.now()
  
  return new Promise((resolve, reject) => {
    const poll = async () => {
      try {
        // 检查是否超时
        if (Date.now() - startTime > maxDuration) {
          reject({})
          return
        }
        
        // 请求结果接口
        const result = await aiResult.GET(`/flow/getResult/$${id}`, {}, null, { hiddenLoading: true })
        // 如果有结果则返回
        if (result?.code === 200) {
          resolve({ value: result?.data ?? {} })
          return
        }
        
        // 如果状态是失败，则直接返回错误
        if (result?.code === 500) {
          reject({})
          return
        }
        
        if (result.code === 300) {
          // 处理中，继续轮询
          setTimeout(poll, pollInterval)
        }
      } catch (error) {
        // 如果是网络错误或其他错误，继续轮询
        if (Date.now() - startTime <= maxDuration) {
          setTimeout(poll, pollInterval)
        } else {
          reject({})
        }
      }
    }
    
    // 开始轮询
    poll()
  })
}